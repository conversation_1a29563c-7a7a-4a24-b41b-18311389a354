.MessagesOrFilesSelector {
    display: flex;
    align-items: center;
    padding: 7px;
    border-bottom: 1px solid rgba(var(--center-channel-color-rgb), 0.08);

    .buttons-container {
        flex-grow: 1;
        margin: 7px 0;
    }

    .tab {
        padding: 4px 12px;
        border: 0;
        border-radius: 4px;
        margin-right: 4px;
        background-color: rgba(var(--center-channel-bg-rgb), 1);
        color: rgba(var(--center-channel-color-rgb), 0.75);
        cursor: pointer;
        font-weight: 600;
        text-decoration: none;

        &.active {
            border-radius: 4px;
            background-color: rgba(var(--button-bg-rgb), 0.08);
            color: var(--button-bg);

            .counter {
                background-color: rgba(var(--button-bg-rgb), 0.08);
            }
        }

        .counter {
            position: relative;
            bottom: 1px;
            padding: 1px 6px;
            border-radius: 8px;
            margin-left: 4px;
            background-color: rgba(var(--center-channel-color-rgb), 0.08);
            font-size: 10px;
            font-weight: 700;

            &:dir(rtl) {
                margin-left: 0;
                margin-right: 4px;
            }
        }
    }
}
select{
  border: none;
  outline: none;
  background: transparent;
.option-team{
    border-color: rgba(var(--button-bg-rgb), 0.08) !important;

}}